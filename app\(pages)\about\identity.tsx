"use client";

import { useState } from "react";

const AboutIdentity = () => {
  const [expandedIndex, setExpandedIndex] = useState<number | null>(null);

  const toggleExpand = (index: number) => {
    if (expandedIndex === index) {
      setExpandedIndex(null);
    } else {
      setExpandedIndex(index);
    }
  };

  const dummyData = [
    {
      title: "who we are?",
      content:
        "At AGKraft, we are a passionate team of developers, designers, and digital experts helping businesses grow through custom websites, mobile apps, and marketing solutions. We blend creativity with technology to deliver user-friendly, scalable, and secure digital experiences.",
    },
    {
      title: "what our goal",
      content:
        "Our goal is simple — to empower startups, businesses, and entrepreneurs with powerful digital tools that make an impact. Whether it's a website, app, or SaaS platform, we focus on delivering real results, not just good-looking designs.",
    },
    {
      title: "our vision",
      content:
        "We envision a future where every business — big or small — can easily go digital without stress. At AGKraft, our vision is to be the trusted tech partner for those who want to innovate, grow, and stand out online.",
    },
  ];

  return (
    <div className="flex justify-center items-center w-full pt-[2rem] px-4 sm:px-6 lg:px-8">
      <div className="flex flex-col lg:flex-row justify-between w-full max-w-[1200px] gap-8 lg:gap-20">
        {/* Left Column - Now full width on mobile, half on desktop */}
        <div className="w-full lg:w-[50%]">
          <h1 className="text-[32px] sm:text-[35px] md:text-[45px] lg:text-[50px] xl:text-[69px] text-white dark:text-white light:text-black font-bold mb-6 lg:mb-8">
            Provided Quality Services from 2025
          </h1>
        </div>

        {/* Right Column - Now full width on mobile, half on desktop */}
        <div className="w-full lg:w-1/2">
          <div className="space-y-4">
            {dummyData.map((item, index) => (
              <div
                key={index}
                className={`border-b-2 border-white dark:border-white light:border-black border-opacity-15 w-full overflow-hidden transition-all duration-300 ${
                  expandedIndex === index ? "pb-4" : ""
                }`}
              >
                <div
                  className="flex justify-between items-center py-4 cursor-pointer group"
                  onClick={() => toggleExpand(index)}
                >
                  <h3 className="w-full font-medium text-[20px] sm:text-[22px] md:text-[24px] lg:text-[26px] xl:text-[28px] leading-[1.5] text-white group-hover:text-opacity-80 transition-colors duration-200">
                    {item.title}
                  </h3>
                  <span className="text-xl sm:text-2xl text-white transition-transform duration-300">
                    {expandedIndex === index ? "-" : "+"}
                  </span>
                </div>
                {expandedIndex === index && (
                  <div className="text-white text-[16px] sm:text-[18px] md:text-[20px] lg:text-[22px] leading-[1.6] opacity-80 animate-fadeIn pl-1 pr-4">
                    {item.content}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AboutIdentity;
