"use client";

import React from "react";
import { useTheme } from "../providers/ThemeProvider";
import { BsSun, BsMoon } from "react-icons/bs";

const ThemeToggle: React.FC = () => {
  const { theme, toggleTheme } = useTheme();

  return (
    <button
      onClick={toggleTheme}
      className="relative flex items-center justify-center w-12 h-12 rounded-full bg-[#2B2B2B] dark:bg-[#2B2B2B] light:bg-white border-2 border-transparent hover:border-[#FF640F] transition-all duration-300 ease-in-out group overflow-hidden"
      aria-label={`Switch to ${theme === "light" ? "dark" : "light"} mode`}
    >
      {/* Sun Icon */}
      <BsSun
        className={`absolute w-5 h-5 text-[#FF640F] transition-all duration-500 ease-in-out transform ${
          theme === "light"
            ? "translate-y-0 opacity-100 rotate-0 scale-100"
            : "translate-y-8 opacity-0 rotate-180 scale-75"
        }`}
      />
      
      {/* Moon Icon */}
      <BsMoon
        className={`absolute w-5 h-5 text-white transition-all duration-500 ease-in-out transform ${
          theme === "dark"
            ? "translate-y-0 opacity-100 rotate-0 scale-100"
            : "-translate-y-8 opacity-0 -rotate-180 scale-75"
        }`}
      />
      
      {/* Hover effect background */}
      <div className="absolute inset-0 rounded-full bg-[#FF640F] opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
    </button>
  );
};

export default ThemeToggle;
